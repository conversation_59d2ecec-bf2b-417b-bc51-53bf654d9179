<?php
#region region DOCS

/** @var Historia|null $historia */
/** @var \App\classes\Proyecto|null $proyecto_asociado */
/** @var string $titulo */
/** @var string $descripcion */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */
/** @var array $validation_errors */

use App\classes\Historia;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Editar Historia</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Editar Historia de Usuario" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
	<!-- BEGIN #app -->
	<div id="app" class="app-header-fixed app-sidebar-fixed">
		<?php #region region HEADER ?>
		<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
		<?php #endregion HEADER ?>

		<?php #region region SIDEBAR ?>
		<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
		<?php #endregion SIDEBAR ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php #region region FLASH MESSAGES ?>
			<?php if ($success_display === 'show'): ?>
				<div class="alert alert-success alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($success_text); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>

			<?php if ($error_display === 'show'): ?>
				<div class="alert alert-danger alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($error_text); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>
			<?php #endregion FLASH MESSAGES ?>

			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">Editar Historia</h4>
					<?php if ($historia): ?>
						<p class="mb-0 text-muted">Editando: <strong><?php echo htmlspecialchars($historia->getTitulo()); ?></strong></p>
						<?php if ($proyecto_asociado): ?>
							<p class="mb-0 text-muted">Proyecto: <strong><?php echo htmlspecialchars($proyecto_asociado->getDescripcion()); ?></strong></p>
						<?php endif; ?>
					<?php else: ?>
						<p class="mb-0 text-muted">Modificar información de la historia de usuario</p>
					<?php endif; ?>
				</div>
				<div class="ms-auto">
					<a href="listado-historias" class="btn btn-outline-primary" onclick="setProjectSession(<?php echo $historia ? $historia->getIdProyecto() : 'null'; ?>)"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Historias</a>
				</div>
			</div>

			<hr>
			<?php #endregion PAGE HEADER ?>

			<?php if ($historia): ?>
			<?php #region region FORM ?>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Información de la Historia</h4>
				</div>
				<div class="panel-body">
					<form method="POST" action="editar-historia" class="needs-validation" novalidate>
						<?php if ($proyecto_asociado): ?>
						<div class="row">
							<div class="col-md-12">
								<div class="mb-3">
									<label for="proyecto" class="form-label">Proyecto</label>
									<input type="text"
									       class="form-control"
									       id="proyecto"
									       value="<?php echo htmlspecialchars($proyecto_asociado->getDescripcion()); ?>"
									       readonly
									       style="background-color: #f8f9fa;">
								</div>
							</div>
						</div>
						<?php endif; ?>

						<div class="row">
							<div class="col-md-12">
								<div class="mb-3">
									<label for="titulo" class="form-label">
										Título <span class="text-danger">*</span>
									</label>
									<input type="text" 
									       class="form-control <?php echo isset($validation_errors['titulo']) ? 'is-invalid' : ''; ?>" 
									       id="titulo" 
									       name="titulo" 
									       value="<?php echo htmlspecialchars($titulo); ?>" 
									       required 
									       maxlength="255"
									       placeholder="Ingrese el título de la historia">
									<?php if (isset($validation_errors['titulo'])): ?>
										<div class="invalid-feedback">
											<?php echo htmlspecialchars($validation_errors['titulo']); ?>
										</div>
									<?php endif; ?>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-12">
								<div class="mb-3">
									<label for="descripcion" class="form-label">
										Descripción <span class="text-danger">*</span>
									</label>
									<textarea class="form-control <?php echo isset($validation_errors['descripcion']) ? 'is-invalid' : ''; ?>" 
									          id="descripcion" 
									          name="descripcion" 
									          rows="5" 
									          required
									          placeholder="Ingrese la descripción detallada de la historia"><?php echo htmlspecialchars($descripcion); ?></textarea>
									<?php if (isset($validation_errors['descripcion'])): ?>
										<div class="invalid-feedback">
											<?php echo htmlspecialchars($validation_errors['descripcion']); ?>
										</div>
									<?php endif; ?>
								</div>
							</div>
						</div>

						<?php if ($historia): ?>
						<div class="row">
							<div class="col-md-6">
								<div class="mb-3">
									<label class="form-label">Progreso Actual</label>
									<div class="progress" style="height: 25px;">
										<div class="progress-bar bg-primary" role="progressbar" 
										     style="width: <?php echo $historia->getPorcProgreso(); ?>%" 
										     aria-valuenow="<?php echo $historia->getPorcProgreso(); ?>" 
										     aria-valuemin="0" 
										     aria-valuemax="100">
											<?php echo number_format($historia->getPorcProgreso(), 1); ?>%
										</div>
									</div>
									<small class="text-muted">
										<i class="fa fa-info-circle me-1"></i>
										Para modificar el progreso, use la opción "Editar Progreso" en la página de listado.
									</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="mb-3">
									<label class="form-label">Estado</label>
									<div class="form-control-plaintext">
										<span class="badge bg-<?php echo $historia->isActiva() ? 'success' : 'secondary'; ?>">
											<?php echo $historia->isActiva() ? 'Activa' : 'Inactiva'; ?>
										</span>
									</div>
								</div>
							</div>
						</div>
						<?php endif; ?>

						<div class="row">
							<div class="col-md-12">
								<div class="mb-3">
									<small class="text-muted">
										<i class="fa fa-info-circle me-1"></i>
										Los campos marcados con <span class="text-danger">*</span> son obligatorios.
									</small>
								</div>
							</div>
						</div>

						<div class="d-flex justify-content-end gap-2">
							<a href="listado-historias" class="btn btn-secondary">
								<i class="fa fa-times fa-fw me-1"></i> Cancelar
							</a>
							<button type="submit" class="btn btn-primary">
								<i class="fa fa-save fa-fw me-1"></i> Actualizar Historia
							</button>
						</div>
					</form>
				</div>
			</div>
			<?php #endregion FORM ?>
			<?php endif; ?>

		</div>
		<!-- END #content -->
	</div>
	<!-- END #app -->

	<?php #region region SCRIPTS ?>
	<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
	
	<script>
		// Auto-focus on titulo field
		document.addEventListener('DOMContentLoaded', function() {
			document.getElementById('titulo').focus();
		});

		// Bootstrap form validation
		(function() {
			'use strict';
			window.addEventListener('load', function() {
				var forms = document.getElementsByClassName('needs-validation');
				var validation = Array.prototype.filter.call(forms, function(form) {
					form.addEventListener('submit', function(event) {
						if (form.checkValidity() === false) {
							event.preventDefault();
							event.stopPropagation();
						}
						form.classList.add('was-validated');
					}, false);
				});
			}, false);
		})();

		// Function to set project session for navigation
		function setProjectSession(projectId) {
			if (projectId && projectId !== 'null') {
				// Set project ID in session via AJAX
				fetch('editar-historia', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded',
					},
					body: 'action=set_project_session&proyecto_id=' + projectId + '&is_ajax=1'
				});
			}
		}
	</script>
	<?php #endregion SCRIPTS ?>
</body>
</html>
