<?php
#region region DOCS

/** @var Historia[] $historias_activas */
/** @var Tarea[] $tareas_historia */
/** @var int|null $filtro_historia_id */
/** @var Historia|null $historia_seleccionada */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Tarea;
use App\classes\Historia;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Gestión de Historias</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de Historias de Usuario" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
	<!-- BEGIN #app -->
	<div id="app" class="app-header-fixed app-sidebar-fixed">
		<?php #region region HEADER ?>
		<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
		<?php #endregion HEADER ?>

		<?php #region region SIDEBAR ?>
		<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
		<?php #endregion SIDEBAR ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php #region region FLASH MESSAGES ?>
			<?php if ($success_display === 'show'): ?>
				<div class="alert alert-success alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($success_text); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>

			<?php if ($error_display === 'show'): ?>
				<div class="alert alert-danger alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($error_text); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>
			<?php #endregion FLASH MESSAGES ?>

			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">Gestión de Historias</h4>
					<p class="mb-0 text-muted">Administración de historias de usuario y tareas asociadas</p>
				</div>
				<div class="ms-auto">
					<a href="ltareas" class="btn btn-outline-primary"><i class="fa fa-tasks fa-fw me-1"></i> Ver Tareas</a>
				</div>
			</div>

			<hr>
			<?php #endregion PAGE HEADER ?>

			<?php #region region HISTORIA FILTER ?>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious d-flex justify-content-between align-items-center">
					<h4 class="panel-title mb-0">Seleccionar Historia</h4>
					<div>
						<a href="ingresar-historia" class="btn btn-sm btn-primary"><i class="fa fa-plus fa-fw me-1"></i> Agregar Historia</a>
					</div>
				</div>
				<div class="panel-body">
					<form method="POST" action="listado-historias" class="row g-3">
						<div class="col-md-6">
							<label for="historia" class="form-label">Historia</label>
							<select class="form-select" id="historia" name="historia" onchange="this.form.submit()">
								<option value="">Seleccione una historia...</option>
								<?php foreach ($historias_activas as $historia): ?>
									<option value="<?php echo $historia->getId(); ?>" 
									        <?php echo $filtro_historia_id == $historia->getId() ? 'selected' : ''; ?>>
										<?php echo htmlspecialchars($historia->getTitulo()); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
					</form>
				</div>
			</div>
			<?php #endregion HISTORIA FILTER ?>

			<?php if ($historia_seleccionada): ?>
			<?php #region region HISTORIA DETAILS ?>
			<div class="panel panel-inverse no-border-radious mt-3">
				<div class="panel-heading no-border-radious d-flex justify-content-between align-items-center">
					<h4 class="panel-title mb-0">
						<?php echo htmlspecialchars($historia_seleccionada->getTitulo()); ?>
					</h4>
					<div>
						<button type="button" class="btn btn-sm btn-warning me-2" onclick="editarProgreso(<?php echo $historia_seleccionada->getId(); ?>, <?php echo $historia_seleccionada->getPorcProgreso(); ?>)">
							<i class="fa fa-edit fa-fw me-1"></i> Editar Progreso
						</button>
						<a href="editar-historia" class="btn btn-sm btn-primary" onclick="setHistoriaSession(<?php echo $historia_seleccionada->getId(); ?>)">
							<i class="fa fa-edit fa-fw me-1"></i> Editar Historia
						</a>
					</div>
				</div>
				<div class="panel-body">
					<?php if ($historia_seleccionada->getDescripcion()): ?>
						<p class="mb-3"><?php echo nl2br(htmlspecialchars($historia_seleccionada->getDescripcion())); ?></p>
					<?php endif; ?>
					
					<!-- Progress Bar -->
					<div class="mb-3">
						<label class="form-label">Progreso: <span id="progress-percentage"><?php echo number_format($historia_seleccionada->getPorcProgreso(), 1); ?>%</span></label>
						<div class="progress" style="height: 25px;">
							<div class="progress-bar bg-primary" role="progressbar" 
							     style="width: <?php echo $historia_seleccionada->getPorcProgreso(); ?>%" 
							     id="progress-bar"
							     aria-valuenow="<?php echo $historia_seleccionada->getPorcProgreso(); ?>" 
							     aria-valuemin="0" 
							     aria-valuemax="100">
								<?php echo number_format($historia_seleccionada->getPorcProgreso(), 1); ?>%
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion HISTORIA DETAILS ?>

			<?php #region region TAREAS TABLE ?>
			<?php if (!empty($tareas_historia)): ?>
			<div class="panel panel-inverse no-border-radious mt-3">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Tareas Asociadas (<?php echo count($tareas_historia); ?>)</h4>
				</div>
				<div class="panel-body">
					<div class="table-responsive">
						<table class="table table-striped table-hover mb-0">
							<thead>
								<tr>
									<th class="text-center" style="width: 80px;">#</th>
									<th>Módulo</th>
									<th>Descripción</th>
									<th class="text-center" style="width: 120px;">Estado</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($tareas_historia as $tarea): ?>
									<tr>
										<td class="text-center align-middle">
											<?php echo $tarea->getId(); ?>
										</td>
										<td class="align-middle">
											<?php echo htmlspecialchars($tarea->getNombreProyectoModulo() ?? 'N/A'); ?>
										</td>
										<td class="align-middle">
											<?php echo htmlspecialchars($tarea->getDescripcion()); ?>
										</td>
										<td class="text-center align-middle">
											<span class="badge" style="background-color: <?php echo $tarea->getBgColor(); ?>; color: white;">
												<?php echo htmlspecialchars($tarea->getNombreTareaEstado()); ?>
											</span>
										</td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<?php else: ?>
			<div class="alert alert-info mt-3">
				<i class="fa fa-info-circle me-2"></i>
				No hay tareas asociadas a esta historia.
			</div>
			<?php endif; ?>
			<?php #endregion TAREAS TABLE ?>

			<?php else: ?>
			<div class="alert alert-info mt-3">
				<i class="fa fa-info-circle me-2"></i>
				Seleccione una historia para ver sus tareas asociadas.
			</div>
			<?php endif; ?>

		</div>
		<!-- END #content -->
	</div>
	<!-- END #app -->

	<?php #region region SCRIPTS ?>
	<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

	<!-- Progress Edit Modal -->
	<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="progressModalLabel">Editar Progreso</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<form id="progressForm">
						<input type="hidden" id="historia_id" name="historia_id">
						<div class="mb-3">
							<label for="nuevo_progreso" class="form-label">Progreso (%)</label>
							<input type="number" class="form-control" id="nuevo_progreso" name="nuevo_progreso"
							       min="0" max="100" step="0.1" required>
							<div class="form-text">Ingrese un valor entre 0 y 100</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="button" class="btn btn-primary" onclick="guardarProgreso()">Guardar</button>
				</div>
			</div>
		</div>
	</div>

	<script>
		function editarProgreso(historiaId, progresoActual) {
			document.getElementById('historia_id').value = historiaId;
			document.getElementById('nuevo_progreso').value = progresoActual;

			var modal = new bootstrap.Modal(document.getElementById('progressModal'));
			modal.show();

			// Auto-focus the progress input field when modal is shown
			document.getElementById('progressModal').addEventListener('shown.bs.modal', function () {
				document.getElementById('nuevo_progreso').focus();
				document.getElementById('nuevo_progreso').select();
			}, { once: true });
		}

		function guardarProgreso() {
			const historiaId = document.getElementById('historia_id').value;
			const nuevoProgreso = document.getElementById('nuevo_progreso').value;

			if (!nuevoProgreso || nuevoProgreso < 0 || nuevoProgreso > 100) {
				swal({
					title: 'Error',
					text: 'Por favor ingrese un valor válido entre 0 y 100',
					icon: 'error',
					button: {
						text: "Cerrar",
						value: null,
						visible: true,
						className: "btn-danger",
						closeModal: true
					}
				});
				return;
			}

			// AJAX request to update progress
			fetch('listado-historias', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded',
				},
				body: `action=update_progress&is_ajax=1&historia_id=${historiaId}&nuevo_progreso=${nuevoProgreso}`
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					// Update progress bar
					const progressBar = document.getElementById('progress-bar');
					const progressPercentage = document.getElementById('progress-percentage');

					progressBar.style.width = data.nuevo_progreso + '%';
					progressBar.setAttribute('aria-valuenow', data.nuevo_progreso);
					progressBar.textContent = parseFloat(data.nuevo_progreso).toFixed(1) + '%';
					progressPercentage.textContent = parseFloat(data.nuevo_progreso).toFixed(1) + '%';

					// Close modal
					bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();

					// Show success toast
					showToast('success', data.message);
				} else {
					swal({
						title: 'Error',
						text: data.message,
						icon: 'error',
						button: {
							text: "Cerrar",
							value: null,
							visible: true,
							className: "btn-danger",
							closeModal: true
						}
					});
				}
			})
			.catch(error => {
				console.error('Error:', error);
				swal({
					title: 'Error',
					text: 'Error de conexión',
					icon: 'error',
					button: {
						text: "Cerrar",
						value: null,
						visible: true,
						className: "btn-danger",
						closeModal: true
					}
				});
			});
		}

		function setHistoriaSession(historiaId) {
			// Set historia ID in session for editing
			fetch('listado-historias', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded',
				},
				body: `action=set_historia_session&is_ajax=1&historia_id=${historiaId}`
			})
			.then(response => {
				if (response.ok) {
					window.location.href = 'editar-historia';
				} else {
					swal({
						title: 'Error',
						text: 'Error al establecer la sesión para editar la historia',
						icon: 'error',
						button: {
							text: "Cerrar",
							value: null,
							visible: true,
							className: "btn-danger",
							closeModal: true
						}
					});
				}
			})
			.catch(error => {
				console.error('Error:', error);
				swal({
					title: 'Error',
					text: 'Error de conexión',
					icon: 'error',
					button: {
						text: "Cerrar",
						value: null,
						visible: true,
						className: "btn-danger",
						closeModal: true
					}
				});
			});
		}

		function showToast(type, message) {
			// Create toast element
			const toastHtml = `
				<div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
					<div class="d-flex">
						<div class="toast-body">
							${message}
						</div>
						<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
					</div>
				</div>
			`;

			// Add to toast container or create one
			let toastContainer = document.querySelector('.toast-container');
			if (!toastContainer) {
				toastContainer = document.createElement('div');
				toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
				document.body.appendChild(toastContainer);
			}

			toastContainer.insertAdjacentHTML('beforeend', toastHtml);
			const toastElement = toastContainer.lastElementChild;
			const toast = new bootstrap.Toast(toastElement);
			toast.show();

			// Remove toast element after it's hidden
			toastElement.addEventListener('hidden.bs.toast', () => {
				toastElement.remove();
			});
		}
	</script>
	<?php #endregion SCRIPTS ?>
</body>
</html>
