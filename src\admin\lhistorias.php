<?php

declare(strict_types=1);

use App\classes\Tarea;
use App\classes\Historia;
use App\classes\ProyectoModulo;
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lhistorias.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

#region region init variables
$historias_activas      = [];
$tareas_historia        = [];
$filtro_historia_id     = filter_input(INPUT_POST, 'historia', FILTER_VALIDATE_INT);
$historia_seleccionada  = null;
$proyecto_seleccionado  = null;
$id_proyecto            = null;
$success_text           = '';
$success_display        = 'none';
$error_text             = '';
$error_display          = 'none';
#endregion init variables

#region region Check Project Session
// Check if project ID is available in session
if (isset($_SESSION['proyecto_id'])) {
	$id_proyecto = (int)$_SESSION['proyecto_id'];
	// Clear the session variable after use
	unset($_SESSION['proyecto_id']);
} else {
	// No project selected, redirect to project selection
	$_SESSION['flash_message_error'] = 'Debe seleccionar un proyecto antes de gestionar historias.';
	header('Location: listado-proyectos');
	exit;
}
#endregion Check Project Session

#region region Handle Flash Message Success
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle AJAX Requests for Progress Update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
	$action = $_POST['action'];
	$isAjax = isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1';

	if ($isAjax && $action === 'update_progress') {
		header('Content-Type: application/json');

		$historia_id = filter_input(INPUT_POST, 'historia_id', FILTER_VALIDATE_INT);
		$nuevo_progreso = filter_input(INPUT_POST, 'nuevo_progreso', FILTER_VALIDATE_FLOAT);

		if (!$historia_id || $nuevo_progreso === false || $nuevo_progreso < 0 || $nuevo_progreso > 100) {
			echo json_encode(['success' => false, 'message' => 'Datos de progreso inválidos']);
			exit;
		}

		try {
			$historia = Historia::get_by_id($historia_id, $conexion);
			if (!$historia) {
				echo json_encode(['success' => false, 'message' => 'Historia no encontrada']);
				exit;
			}

			$historia->setPorcProgreso($nuevo_progreso);
			$resultado = $historia->actualizar($conexion);

			if ($resultado) {
				echo json_encode([
					'success' => true, 
					'message' => 'Progreso actualizado correctamente',
					'nuevo_progreso' => $nuevo_progreso
				]);
			} else {
				echo json_encode(['success' => false, 'message' => 'Error al actualizar el progreso']);
			}
		} catch (Exception $e) {
			echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
		}
		exit;
	}

	if ($isAjax && $action === 'set_historia_session') {
		$historia_id = filter_input(INPUT_POST, 'historia_id', FILTER_VALIDATE_INT);

		if ($historia_id) {
			$_SESSION['historia_edit_id'] = $historia_id;
		}
		exit;
	}

	if ($isAjax && $action === 'set_project_session') {
		$proyecto_id = filter_input(INPUT_POST, 'proyecto_id', FILTER_VALIDATE_INT);

		if ($proyecto_id) {
			$_SESSION['proyecto_id'] = $proyecto_id;
		}
		exit;
	}
}
#endregion Handle AJAX Requests for Progress Update

try {
	// Get the selected project
	$proyecto_seleccionado = Proyecto::get($id_proyecto, $conexion);
	if (!$proyecto_seleccionado) {
		$_SESSION['flash_message_error'] = 'Proyecto no encontrado.';
		header('Location: listado-proyectos');
		exit;
	}

	// Get Historias filtered by project
	$historias_activas = Historia::get_by_proyecto_id($id_proyecto, $conexion);

	// If a Historia is selected, get its tasks
	if ($filtro_historia_id) {
		$historia_seleccionada = Historia::get_by_id($filtro_historia_id, $conexion);
		if ($historia_seleccionada && $historia_seleccionada->isActiva() && $historia_seleccionada->getIdProyecto() === $id_proyecto) {
			// Get tasks associated with this Historia, excluding deleted tasks
			$tareas_historia = Tarea::getTasksByHistoria($filtro_historia_id, $conexion);
		}
	}

} catch (Exception $e) {
	$error_text = "Error al cargar datos: " . $e->getMessage();
	$error_display = 'show';
}

// Include the view
require_once __ROOT__ . '/views/admin/lhistorias.view.php';
